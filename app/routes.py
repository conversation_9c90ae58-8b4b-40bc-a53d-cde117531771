import os
from datetime import datetime
from pathlib import Path
from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, send_file, current_app
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.utils import secure_filename
from app import db, login_manager
from app.models import User, AdaptationTask
from app.forms import LoginForm, UploadForm, QuickAdaptForm
from app.utils import save_uploaded_file, get_file_size_mb
from app.tasks import process_adaptation_task

bp = Blueprint('main', __name__)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

@bp.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    return redirect(url_for('main.login'))

@bp.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user and user.check_password(form.password.data):
            login_user(user)
            flash('登录成功！', 'success')
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('main.dashboard'))
        flash('用户名或密码错误！', 'error')
    
    return render_template('login.html', form=form)

@bp.route('/logout')
@login_required
def logout():
    logout_user()
    flash('已退出登录。', 'info')
    return redirect(url_for('main.login'))

@bp.route('/dashboard')
@login_required
def dashboard():
    # 获取用户的所有任务
    tasks = AdaptationTask.query.filter_by(user_id=current_user.id).order_by(AdaptationTask.created_at.desc()).all()
    return render_template('dashboard.html', tasks=tasks)

@bp.route('/upload', methods=['GET', 'POST'])
@login_required
def upload():
    form = UploadForm()
    if form.validate_on_submit():
        file = form.file.data

        # 保存上传的文件
        file_path = save_uploaded_file(file, current_user.id)
        if not file_path:
            flash('文件上传失败！', 'error')
            return render_template('upload.html', form=form)

        # 创建改编任务
        task_name = f"{form.book_name.data} - {secure_filename(file.filename)}"
        task = AdaptationTask(
            user_id=current_user.id,
            task_name=task_name,
            original_filename=secure_filename(file.filename),
            file_path=file_path,
            character=form.character.data,
            book_name=form.book_name.data,
            channel=form.channel.data,
            person=form.person.data
        )

        db.session.add(task)
        db.session.commit()

        # 启动异步任务
        celery_task = process_adaptation_task.delay(task.id)
        task.celery_task_id = celery_task.id
        db.session.commit()

        flash(f'文件上传成功，开始改编处理！', 'success')
        return redirect(url_for('main.task_detail', task_id=task.id))

    return render_template('upload.html', form=form)

@bp.route('/quick', methods=['GET', 'POST'])
@login_required
def quick_adapt():
    form = QuickAdaptForm()
    if form.validate_on_submit():
        # 创建临时文件保存内容
        import tempfile
        import uuid

        # 创建用户专属目录
        user_dir = Path(current_app.config['UPLOAD_FOLDER']) / str(current_user.id)
        user_dir.mkdir(parents=True, exist_ok=True)

        # 保存内容到临时文件
        temp_filename = f"quick_{uuid.uuid4().hex}.txt"
        temp_path = user_dir / temp_filename

        with open(temp_path, 'w', encoding='utf-8') as f:
            f.write(form.content.data)

        # 创建改编任务
        task_name = f"{form.book_name.data} - 快速改编"
        task = AdaptationTask(
            user_id=current_user.id,
            task_name=task_name,
            original_filename="快速改编内容.txt",
            file_path=str(temp_path),
            character=form.character.data,
            book_name=form.book_name.data,
            channel=form.channel.data,
            person=form.person.data
        )

        db.session.add(task)
        db.session.commit()

        # 启动异步任务
        celery_task = process_adaptation_task.delay(task.id)
        task.celery_task_id = celery_task.id
        db.session.commit()

        flash('内容提交成功，开始改编处理！', 'success')
        return redirect(url_for('main.task_detail', task_id=task.id))

    return render_template('quick.html', form=form)

@bp.route('/task/<int:task_id>')
@login_required
def task_detail(task_id):
    task = AdaptationTask.query.filter_by(id=task_id, user_id=current_user.id).first_or_404()
    return render_template('task_detail.html', task=task)

@bp.route('/api/task/<int:task_id>/status')
@login_required
def task_status(task_id):
    try:
        task = AdaptationTask.query.filter_by(id=task_id, user_id=current_user.id).first_or_404()

        result = {
            'status': task.status,
            'progress': task.progress or 0,
            'total_chapters': task.total_chapters or 0,
            'processed_chapters': task.processed_chapters or 0,
            'created_at': task.created_at.isoformat() if task.created_at else None,
            'started_at': task.started_at.isoformat() if task.started_at else None,
            'completed_at': task.completed_at.isoformat() if task.completed_at else None,
            'error_message': task.error_message
        }

        # 如果有Celery任务ID，获取详细状态
        if task.celery_task_id:
            try:
                from app import celery
                from celery.result import AsyncResult

                # 使用app的celery实例来创建AsyncResult
                celery_result = AsyncResult(task.celery_task_id, app=celery)

                current_app.logger.debug(f"Celery task {task.celery_task_id} state: {celery_result.state}")

                if celery_result.state == 'PROGRESS':
                    if celery_result.info and isinstance(celery_result.info, dict):
                        # 只更新有用的字段
                        for key in ['current', 'total', 'status', 'chapters_current', 'chapters_total']:
                            if key in celery_result.info:
                                result[key] = celery_result.info[key]
                        current_app.logger.debug(f"Updated result with Celery info: {celery_result.info}")
                elif celery_result.state == 'FAILURE':
                    error_info = str(celery_result.info) if celery_result.info else "未知错误"
                    result['error_message'] = error_info
                    current_app.logger.error(f"Celery task failed: {error_info}")
                elif celery_result.state == 'SUCCESS':
                    # 任务成功完成，确保数据库状态是最新的
                    if task.status != 'completed':
                        task.status = 'completed'
                        task.progress = 100
                        db.session.commit()
                        result['status'] = 'completed'
                        result['progress'] = 100
                elif celery_result.state == 'PENDING':
                    # 任务还在等待中
                    result['status'] = 'pending'
                    current_app.logger.debug(f"Celery task {task.celery_task_id} is still pending")

            except Exception as celery_error:
                current_app.logger.error(f"Error getting Celery task status: {str(celery_error)}")
                # 不要因为Celery错误而影响基本状态返回
                result['celery_error'] = str(celery_error)

        return jsonify(result)

    except Exception as e:
        current_app.logger.error(f"Error in task_status API: {str(e)}")
        return jsonify({'error': '获取任务状态失败', 'message': str(e)}), 500

@bp.route('/api/debug/task/<int:task_id>')
@login_required
def debug_task_status(task_id):
    """调试用的任务状态接口"""
    try:
        task = AdaptationTask.query.filter_by(id=task_id, user_id=current_user.id).first_or_404()

        debug_info = {
            'task_id': task.id,
            'task_name': task.task_name,
            'status': task.status,
            'progress': task.progress,
            'celery_task_id': task.celery_task_id,
            'error_message': task.error_message,
            'created_at': task.created_at.isoformat() if task.created_at else None,
        }

        if task.celery_task_id:
            try:
                from app import celery
                from celery.result import AsyncResult
                celery_result = AsyncResult(task.celery_task_id, app=celery)

                debug_info['celery_state'] = celery_result.state
                debug_info['celery_info'] = str(celery_result.info)
                debug_info['celery_ready'] = celery_result.ready()
                debug_info['celery_successful'] = celery_result.successful() if celery_result.ready() else None

            except Exception as e:
                debug_info['celery_error'] = str(e)

        return jsonify(debug_info)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@bp.route('/download/<int:task_id>')
@login_required
def download_result(task_id):
    task = AdaptationTask.query.filter_by(id=task_id, user_id=current_user.id).first_or_404()
    
    if not task.is_completed or not task.output_path:
        flash('任务尚未完成或没有输出文件！', 'error')
        return redirect(url_for('main.task_detail', task_id=task_id))
    
    if not os.path.exists(task.output_path):
        flash('输出文件不存在！', 'error')
        return redirect(url_for('main.task_detail', task_id=task_id))
    
    return send_file(
        task.output_path,
        as_attachment=True,
        download_name=f"{task.task_name}_adapted.txt"
    )

@bp.route('/delete_task/<int:task_id>', methods=['POST'])
@login_required
def delete_task(task_id):
    task = AdaptationTask.query.filter_by(id=task_id, user_id=current_user.id).first_or_404()
    
    # 如果任务正在处理中，尝试取消
    if task.celery_task_id and task.status == 'processing':
        from celery.result import AsyncResult
        celery_result = AsyncResult(task.celery_task_id)
        celery_result.revoke(terminate=True)
    
    # 删除相关文件
    try:
        if task.file_path and os.path.exists(task.file_path):
            os.remove(task.file_path)
        if task.output_path and os.path.exists(task.output_path):
            os.remove(task.output_path)
    except Exception as e:
        current_app.logger.error(f"删除文件失败: {str(e)}")
    
    # 删除数据库记录
    db.session.delete(task)
    db.session.commit()
    
    flash('任务已删除！', 'success')
    return redirect(url_for('main.dashboard'))
